<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>React CDN模板 | PHP支付系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    
    <!-- 保留原有CSS框架 -->
    <link rel="stylesheet" href="<?php echo $cdnpublic?>twitter-bootstrap/3.4.1/css/bootstrap.min.css" type="text/css" />
    <link rel="stylesheet" href="<?php echo $cdnpublic?>font-awesome/4.7.0/css/font-awesome.min.css" type="text/css" />
    
    <!-- React CDN资源 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Ant Design CDN -->
    <link rel="stylesheet" href="https://unpkg.com/antd@5/dist/reset.css">
    <script src="https://unpkg.com/antd@5/dist/antd.min.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        /* React组件样式覆盖 */
        .react-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .react-form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 400px;
        }
        
        /* 保持原有功能的隐藏表单 */
        .original-form {
            display: none;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .react-form-container {
                margin: 1rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- React渲染容器 -->
    <div id="react-app"></div>
    
    <!-- 保留原有PHP表单（隐藏，用于提交数据） -->
    <div class="original-form">
        <form id="original-form" method="post" action="">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token?>">
            <input type="hidden" name="user" id="hidden-user">
            <input type="hidden" name="pass" id="hidden-pass">
            <!-- 其他原有字段 -->
        </form>
    </div>
    
    <!-- 保留原有JavaScript库 -->
    <script src="<?php echo $cdnpublic?>jquery/3.4.1/jquery.min.js"></script>
    <script src="<?php echo $cdnpublic?>layer/3.1.1/layer.js"></script>
    
    <!-- React组件定义 -->
    <script type="text/babel">
        const { useState, useEffect } = React;
        const { Button, Input, Form, Card, Typography, Space, Alert } = antd;
        const { Title } = Typography;
        
        // 主要React组件
        function LoginForm() {
            const [loading, setLoading] = useState(false);
            const [form] = Form.useForm();
            
            // 提交处理函数（保持原有逻辑）
            const handleSubmit = async (values) => {
                setLoading(true);
                
                // 将数据填入隐藏表单
                document.getElementById('hidden-user').value = values.username;
                document.getElementById('hidden-pass').value = values.password;
                
                // 调用原有的提交函数
                if (window.submitLogin) {
                    window.submitLogin('user', values.username, values.password);
                } else {
                    // 备用提交方式
                    document.getElementById('original-form').submit();
                }
                
                setLoading(false);
            };
            
            return (
                <div className="react-container">
                    <div style={{ padding: '2rem 0', textAlign: 'center' }}>
                        <Card className="react-form-container">
                            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                                <div style={{ textAlign: 'center' }}>
                                    <Title level={2} style={{ color: '#1890ff' }}>
                                        <?php echo $conf['sitename']?>
                                    </Title>
                                    <p style={{ color: '#666' }}>请输入您的登录信息</p>
                                </div>
                                
                                <Form
                                    form={form}
                                    layout="vertical"
                                    onFinish={handleSubmit}
                                    size="large"
                                >
                                    <Form.Item
                                        name="username"
                                        label="用户名"
                                        rules={[{ required: true, message: '请输入用户名' }]}
                                    >
                                        <Input 
                                            prefix={<i className="fa fa-user" />}
                                            placeholder="请输入用户名"
                                        />
                                    </Form.Item>
                                    
                                    <Form.Item
                                        name="password"
                                        label="密码"
                                        rules={[{ required: true, message: '请输入密码' }]}
                                    >
                                        <Input.Password 
                                            prefix={<i className="fa fa-lock" />}
                                            placeholder="请输入密码"
                                        />
                                    </Form.Item>
                                    
                                    <Form.Item>
                                        <Button 
                                            type="primary" 
                                            htmlType="submit" 
                                            loading={loading}
                                            block
                                            size="large"
                                        >
                                            登录
                                        </Button>
                                    </Form.Item>
                                </Form>
                                
                                <div style={{ textAlign: 'center' }}>
                                    <Space>
                                        <a href="reg.php">注册账号</a>
                                        <a href="findpwd.php">忘记密码</a>
                                    </Space>
                                </div>
                            </Space>
                        </Card>
                    </div>
                </div>
            );
        }
        
        // 渲染React应用
        ReactDOM.render(<LoginForm />, document.getElementById('react-app'));
    </script>
    
    <!-- 保留原有的JavaScript函数 -->
    <script>
        // 保持原有的submitLogin函数等
        // 这里会保留所有原有的JavaScript逻辑
    </script>
</body>
</html>
