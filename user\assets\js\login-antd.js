// Ant Design 登录组件
// 全局变量
let captchaObj = null;
let captchaResult = null;

// 验证码处理函数
const handlerEmbed = function (obj) {
    captchaObj = obj;
    obj.appendTo('#captcha-container');
    obj.onReady(function () {
        console.log('验证码加载完成');
    }).onSuccess(function () {
        const result = obj.getValidate();
        if (!result) {
            antd.message.error('请完成验证');
            return;
        }
        captchaResult = result;
        antd.message.success('验证完成');
    });
};

// 主登录组件
const AntdLoginApp = function() {
    const useState = React.useState;
    const useEffect = React.useEffect;
    const Form = antd.Form;
    const Input = antd.Input;
    const Button = antd.Button;
    const Tabs = antd.Tabs;
    const Checkbox = antd.Checkbox;
    const Space = antd.Space;
    const Divider = antd.Divider;
    const Spin = antd.Spin;
    const message = antd.message;
    
    const formRef = Form.useForm();
    const form = formRef[0];
    
    const loadingState = useState(false);
    const loading = loadingState[0];
    const setLoading = loadingState[1];
    
    const loginModeState = useState('password');
    const loginMode = loginModeState[0];
    const setLoginMode = loginModeState[1];
    
    const captchaLoadingState = useState(false);
    const captchaLoading = captchaLoadingState[0];
    const setCaptchaLoading = captchaLoadingState[1];
    
    // 获取页面配置
    const getConfig = function(name) {
        const input = document.querySelector('input[name="' + name + '"]');
        return input ? input.value : '';
    };
    
    const config = {
        csrfToken: getConfig('csrf_token'),
        closeKeyLogin: getConfig('close_keylogin') === '1',
        captchaOpen: getConfig('captcha_open') === '1',
        regOpen: getConfig('reg_open') !== '0',
        loginAlipay: getConfig('login_alipay'),
        loginQQ: getConfig('login_qq'),
        loginWX: getConfig('login_wx'),
        connectMode: getConfig('connect_mode') === '1'
    };
    
    // 初始化验证码
    useEffect(function() {
        if (config.captchaOpen) {
            setCaptchaLoading(true);
            $.ajax({
                url: "ajax.php?act=captcha",
                type: "get",
                cache: false,
                dataType: "json",
                success: function (data) {
                    setCaptchaLoading(false);
                    if(data.version == 1){
                        initGeetest4({
                            captchaId: data.gt,
                            product: 'popup',
                            protocol: 'https://',
                            riskType: 'slide',
                            hideSuccess: true,
                            nativeButton: {width: '100%'}
                        }, handlerEmbed);
                    }else{
                        initGeetest({
                            gt: data.gt,
                            challenge: data.challenge,
                            new_captcha: data.new_captcha,
                            product: "popup",
                            width: "100%",
                            offline: !data.success,
                        }, handlerEmbed);
                    }
                },
                error: function() {
                    setCaptchaLoading(false);
                    message.error('验证码加载失败');
                }
            });
        }
    }, [config.captchaOpen]);
    
    // 登录提交
    const handleLogin = function(values) {
        if (config.captchaOpen && !captchaResult) {
            message.error('请先完成滑动验证！');
            return;
        }
        
        setLoading(true);
        
        const requestData = {
            type: loginMode === 'password' ? 1 : 0,
            user: values.username,
            pass: values.password,
            csrf_token: config.csrfToken
        };
        
        if (captchaResult) {
            Object.assign(requestData, captchaResult);
        }
        
        $.ajax({
            type: "POST",
            dataType: "json",
            data: requestData,
            url: "ajax.php?act=login",
            success: function(response) {
                setLoading(false);
                if (response.code === 0) {
                    message.success(response.msg);
                    setTimeout(function() {
                        window.location.href = response.url;
                    }, 1000);
                } else {
                    message.error(response.msg);
                    if (captchaObj) {
                        captchaObj.reset();
                        captchaResult = null;
                    }
                }
            },
            error: function() {
                setLoading(false);
                message.error('服务器错误');
            }
        });
    };
    
    // 第三方登录
    const handleSocialLogin = function(type) {
        $.ajax({
            type: "POST",
            url: "ajax.php?act=connect",
            data: { type: type },
            dataType: 'json',
            success: function(data) {
                if(data.code === 0){
                    window.location.href = data.url;
                }else{
                    message.error(data.msg);
                }
            },
            error: function() {
                message.error('连接失败');
            }
        });
    };
    
    // 渲染用户名输入框
    const renderUsernameInput = function() {
        if (loginMode === 'password') {
            return React.createElement(Form.Item, {
                name: 'username',
                rules: [{ required: true, message: '请输入邮箱/手机号' }]
            }, React.createElement(Input, {
                prefix: React.createElement('i', { className: 'fa fa-user' }),
                placeholder: '邮箱/手机号',
                size: 'large'
            }));
        } else {
            return React.createElement(Form.Item, {
                name: 'username',
                rules: [{ required: true, message: '请输入商户ID' }]
            }, React.createElement(Input, {
                prefix: React.createElement('i', { className: 'fa fa-user' }),
                placeholder: '商户ID',
                size: 'large'
            }));
        }
    };
    
    // 构建表单元素数组
    const formElements = [];
    
    // 登录方式切换
    if (!config.closeKeyLogin) {
        formElements.push(React.createElement(Tabs, {
            activeKey: loginMode,
            onChange: setLoginMode,
            centered: true,
            items: [
                { key: 'password', label: '密码登录' },
                { key: 'key', label: '密钥登录' }
            ]
        }));
    }
    
    // 用户名输入框
    formElements.push(renderUsernameInput());
    
    // 密码输入框
    formElements.push(React.createElement(Form.Item, {
        name: 'password',
        rules: [{ required: true, message: loginMode === 'password' ? '请输入密码' : '请输入商户密钥' }]
    }, React.createElement(Input.Password, {
        prefix: React.createElement('i', { className: 'fa fa-lock' }),
        placeholder: loginMode === 'password' ? '密码' : '商户密钥',
        size: 'large'
    })));
    
    // 验证码
    if (config.captchaOpen) {
        formElements.push(React.createElement(Form.Item, null,
            React.createElement('div', { className: 'captcha-container' },
                captchaLoading ? 
                    React.createElement('div', { style: { textAlign: 'center', padding: '20px' } },
                        React.createElement(Spin, { tip: '正在加载验证码...' })
                    ) :
                    React.createElement('div', { id: 'captcha-container' })
            )
        ));
    }
    
    // 记住登录状态
    formElements.push(React.createElement(Form.Item, null,
        React.createElement(Checkbox, null, '记住登录状态')
    ));
    
    // 登录按钮
    formElements.push(React.createElement(Form.Item, null,
        React.createElement(Button, {
            type: 'primary',
            htmlType: 'submit',
            loading: loading,
            block: true,
            size: 'large'
        }, '立即登录')
    ));
    
    // 找回密码和注册链接
    const linkElements = [
        React.createElement(Button, { 
            key: 'findpwd',
            type: 'link', 
            href: 'findpwd.php' 
        }, React.createElement('i', { className: 'fa fa-unlock' }), ' 找回密码')
    ];
    
    if (config.regOpen) {
        linkElements.push(React.createElement(Button, { 
            key: 'reg',
            type: 'link', 
            href: 'reg.php' 
        }, React.createElement('i', { className: 'fa fa-user-plus' }), ' 注册商户'));
    }
    
    formElements.push(React.createElement(Form.Item, null,
        React.createElement(Space, { 
            style: { width: '100%', justifyContent: 'space-between' } 
        }, linkElements)
    ));
    
    // 构建主要内容数组
    const mainElements = [];
    
    // 添加表单
    const formProps = {
        form: form,
        onFinish: handleLogin,
        layout: 'vertical',
        size: 'large'
    };
    const formArgs = [Form, formProps].concat(formElements);
    mainElements.push(React.createElement.apply(React, formArgs));
    
    // 添加第三方登录
    if (!config.connectMode && (config.loginAlipay > 0 || config.loginQQ > 0 || config.loginWX > 0)) {
        const socialButtons = [];
        
        if (config.loginAlipay > 0 || config.loginAlipay == -1) {
            socialButtons.push(React.createElement(Button, {
                key: 'alipay',
                className: 'social-btn',
                onClick: function() { handleSocialLogin('alipay'); },
                title: '支付宝快捷登录'
            }, React.createElement('img', { src: '../assets/icon/alipay.ico', width: '20', alt: '支付宝' })));
        }
        
        if (config.loginQQ > 0) {
            socialButtons.push(React.createElement(Button, {
                key: 'qq',
                className: 'social-btn',
                onClick: function() { handleSocialLogin('qq'); },
                title: 'QQ快捷登录'
            }, React.createElement('i', { className: 'fa fa-qq', style: { color: '#0BB2FF', fontSize: '20px' } })));
        }
        
        if (config.loginWX > 0 || config.loginWX == -1) {
            socialButtons.push(React.createElement(Button, {
                key: 'wx',
                className: 'social-btn',
                onClick: function() { handleSocialLogin('wx'); },
                title: '微信快捷登录'
            }, React.createElement('i', { className: 'fa fa-wechat', style: { color: 'green', fontSize: '20px' } })));
        }
        
        const socialArgs = [Space, null].concat(socialButtons);
        mainElements.push(React.createElement('div', { className: 'social-login' },
            React.createElement(Divider, null, '第三方登录'),
            React.createElement.apply(React, socialArgs)
        ));
    }
    
    const mainArgs = ['div', null].concat(mainElements);
    return React.createElement.apply(React, mainArgs);
};

// 初始化应用
function initAntdLogin() {
    const container = document.getElementById('antd-login-app');
    if (container && typeof React !== 'undefined' && typeof antd !== 'undefined') {
        const root = ReactDOM.createRoot(container);
        root.render(React.createElement(AntdLoginApp));
        console.log('Ant Design登录组件已初始化');
    } else {
        console.error('React或Ant Design未加载');
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAntdLogin);
} else {
    initAntdLogin();
}
