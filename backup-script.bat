@echo off
chcp 65001 >nul
echo ========================================
echo PHP支付系统文件备份脚本
echo ========================================
echo.

set "timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "timestamp=%timestamp: =0%"

if not exist "backups" mkdir backups
if not exist "backups\%timestamp:~0,8%" mkdir "backups\%timestamp:~0,8%"

echo 开始备份文件...
echo 备份时间: %timestamp%
echo.

REM 备份用户端关键文件
echo [1/5] 备份用户端文件...
if exist "user\login.php" (
    copy "user\login.php" "user\login.php.backup.%timestamp%" >nul
    copy "user\login.php" "backups\%timestamp:~0,8%\login.php.backup.%timestamp%" >nul
    echo   ✓ user\login.php 备份完成
)

if exist "user\reg.php" (
    copy "user\reg.php" "user\reg.php.backup.%timestamp%" >nul
    copy "user\reg.php" "backups\%timestamp:~0,8%\reg.php.backup.%timestamp%" >nul
    echo   ✓ user\reg.php 备份完成
)

if exist "user\index.php" (
    copy "user\index.php" "user\index.php.backup.%timestamp%" >nul
    copy "user\index.php" "backups\%timestamp:~0,8%\index.php.backup.%timestamp%" >nul
    echo   ✓ user\index.php 备份完成
)

REM 备份支付页面
echo [2/5] 备份支付页面...
if exist "paypage\index.php" (
    copy "paypage\index.php" "paypage\index.php.backup.%timestamp%" >nul
    copy "paypage\index.php" "backups\%timestamp:~0,8%\paypage_index.php.backup.%timestamp%" >nul
    echo   ✓ paypage\index.php 备份完成
)

REM 备份管理后台
echo [3/5] 备份管理后台...
if exist "admin\login.php" (
    copy "admin\login.php" "admin\login.php.backup.%timestamp%" >nul
    copy "admin\login.php" "backups\%timestamp:~0,8%\admin_login.php.backup.%timestamp%" >nul
    echo   ✓ admin\login.php 备份完成
)

REM 备份配置文件
echo [4/5] 备份配置文件...
if exist "config.php" (
    copy "config.php" "config.php.backup.%timestamp%" >nul
    copy "config.php" "backups\%timestamp:~0,8%\config.php.backup.%timestamp%" >nul
    echo   ✓ config.php 备份完成
)

REM 备份重要目录
echo [5/5] 备份资源目录...
if exist "user\assets" (
    xcopy "user\assets" "backups\%timestamp:~0,8%\user_assets\" /E /I /Q >nul
    echo   ✓ user\assets 目录备份完成
)

echo.
echo ========================================
echo 备份完成！
echo 备份位置: 
echo   - 本地备份: 各文件同目录下 .backup.%timestamp% 文件
echo   - 统一备份: backups\%timestamp:~0,8%\ 目录
echo ========================================
echo.
pause
