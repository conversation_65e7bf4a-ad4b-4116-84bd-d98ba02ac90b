<?php
/**
 * 登录
**/
$is_defend=true;
include("../includes/common.php");

if(isset($_GET['logout'])){
	if(!checkRefererHost())exit();
	setcookie("user_token", "", time() - 2592000);
	@header('Content-Type: text/html; charset=UTF-8');
	exit("<script language='javascript'>alert('您已成功注销本次登录！');window.location.href='./login.php';</script>");
}elseif($islogin2==1){
	exit("<script language='javascript'>alert('您已登录！');window.location.href='./';</script>");
}
$csrf_token = md5(mt_rand(0,999).time());
$_SESSION['csrf_token'] = $csrf_token;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8" />
<title>登录 | <?php echo $conf['sitename']?></title>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
<!-- Ant Design CSS -->
<link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/reset.css">
<link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/antd.min.css">

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="<?php echo $cdnpublic?>font-awesome/4.7.0/css/font-awesome.min.css" type="text/css" />

<!-- 自定义样式 -->
<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

.login-container {
    max-width: 400px;
    margin: 50px auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.login-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.login-header p {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.login-content {
    padding: 30px;
}

.social-login {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.login-footer {
    text-align: center;
    padding: 20px;
    background: #fafafa;
    color: #666;
    font-size: 12px;
}

.captcha-container {
    margin: 15px 0;
}

/* 自定义Ant Design样式 */
.ant-form-item {
    margin-bottom: 16px;
}

.ant-input-affix-wrapper {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s;
}

.ant-input-affix-wrapper:hover {
    border-color: #40a9ff;
}

.ant-input-affix-wrapper-focused {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    border: none;
    border-radius: 8px;
    height: 40px;
    font-weight: 500;
    transition: all 0.3s;
}

.ant-btn-primary:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #9254de 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.ant-tabs-tab {
    font-weight: 500;
}

.social-btn {
    margin: 0 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

input:-webkit-autofill{
    -webkit-box-shadow:0 0 0px 1000px white inset;
    -webkit-text-fill-color:#333;
}
</style>
</head>
<body>
<div class="login-container">
    <!-- 页面头部 -->
    <div class="login-header">
        <h1><?php echo $conf['sitename']?></h1>
        <p>请输入您的商户信息</p>
    </div>

    <!-- 登录内容区域 -->
    <div class="login-content">
        <!-- React 组件挂载点 -->
        <div id="antd-login-app"></div>

        <!-- 隐藏的原始表单数据 -->
        <form style="display: none;">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token?>">
            <input type="hidden" name="login_mode" value="<?php echo $_GET['m']=='key'?'key':'password'?>">
            <input type="hidden" name="close_keylogin" value="<?php echo $conf['close_keylogin']?>">
            <input type="hidden" name="captcha_open" value="<?php echo $conf['captcha_open_login']?>">
            <input type="hidden" name="reg_open" value="<?php echo $conf['reg_open']?>">
            <input type="hidden" name="login_alipay" value="<?php echo $conf['login_alipay']?>">
            <input type="hidden" name="login_qq" value="<?php echo $conf['login_qq']?>">
            <input type="hidden" name="login_wx" value="<?php echo $conf['login_wx']?>">
            <input type="hidden" name="connect_mode" value="<?php echo isset($_GET['connect'])?'1':'0'?>">
        </form>
    </div>

    <!-- 页面底部 -->
    <div class="login-footer">
        <a href="/"><?php echo $conf['sitename']?></a><br>
        &copy; 2016~<?php echo date("Y")?>
    </div>
</div>
<!-- React & Ant Design CDN -->
<script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
<script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
<script crossorigin src="https://unpkg.com/dayjs@1.11.10/dayjs.min.js"></script>
<script crossorigin src="https://unpkg.com/antd@5.12.8/dist/antd.min.js"></script>
<script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

<!-- 原有依赖 -->
<script src="<?php echo $cdnpublic?>jquery/3.4.1/jquery.min.js"></script>
<script src="<?php echo $cdnpublic?>layer/3.1.1/layer.js"></script>
<script src="//static.geetest.com/static/tools/gt.js"></script>
<script>
window.appendChildOrg = Element.prototype.appendChild;
Element.prototype.appendChild = function() {
    if(arguments[0].tagName == 'SCRIPT'){
        arguments[0].setAttribute('referrerpolicy', 'no-referrer');
    }
    return window.appendChildOrg.apply(this, arguments);
};
</script>
<script src="//static.geetest.com/v4/gt4.js"></script>

<!-- Ant Design 登录组件 -->
<script type="text/babel">
const { useState, useEffect } = React;
const {
    Form, Input, Button, Tabs, Checkbox, Space, Divider,
    message, Spin, Alert
} = antd;

// 全局变量
let captchaObj = null;
let captchaResult = null;

// 验证码处理函数
const handlerEmbed = function (obj) {
    captchaObj = obj;
    obj.appendTo('#captcha-container');
    obj.onReady(function () {
        console.log('验证码加载完成');
    }).onSuccess(function () {
        const result = obj.getValidate();
        if (!result) {
            message.error('请完成验证');
            return;
        }
        captchaResult = result;
        message.success('验证完成');
    });
};

// 主登录组件
const AntdLoginApp = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [loginMode, setLoginMode] = useState('password');
    const [captchaLoading, setCaptchaLoading] = useState(false);

    // 获取页面配置
    const getConfig = (name) => {
        const input = document.querySelector(`input[name="${name}"]`);
        return input ? input.value : '';
    };

    const config = {
        csrfToken: getConfig('csrf_token'),
        closeKeyLogin: getConfig('close_keylogin') === '1',
        captchaOpen: getConfig('captcha_open') === '1',
        regOpen: getConfig('reg_open') !== '0',
        loginAlipay: getConfig('login_alipay'),
        loginQQ: getConfig('login_qq'),
        loginWX: getConfig('login_wx'),
        connectMode: getConfig('connect_mode') === '1'
    };

    // 初始化验证码
    useEffect(() => {
        if (config.captchaOpen) {
            setCaptchaLoading(true);
            $.ajax({
                url: "ajax.php?act=captcha",
                type: "get",
                cache: false,
                dataType: "json",
                success: function (data) {
                    setCaptchaLoading(false);
                    if(data.version == 1){
                        initGeetest4({
                            captchaId: data.gt,
                            product: 'popup',
                            protocol: 'https://',
                            riskType: 'slide',
                            hideSuccess: true,
                            nativeButton: {width: '100%'}
                        }, handlerEmbed);
                    }else{
                        initGeetest({
                            gt: data.gt,
                            challenge: data.challenge,
                            new_captcha: data.new_captcha,
                            product: "popup",
                            width: "100%",
                            offline: !data.success,
                        }, handlerEmbed);
                    }
                },
                error: function() {
                    setCaptchaLoading(false);
                    message.error('验证码加载失败');
                }
            });
        }
    }, [config.captchaOpen]);

    // 登录提交
    const handleLogin = (values) => {
        if (config.captchaOpen && !captchaResult) {
            message.error('请先完成滑动验证！');
            return;
        }

        setLoading(true);

        $.ajax({
            type: "POST",
            dataType: "json",
            data: {
                type: loginMode === 'password' ? 1 : 0,
                user: values.username,
                pass: values.password,
                csrf_token: config.csrfToken,
                ...(captchaResult || {})
            },
            url: "ajax.php?act=login",
            success: function(response) {
                setLoading(false);
                if (response.code === 0) {
                    message.success(response.msg);
                    setTimeout(() => {
                        window.location.href = response.url;
                    }, 1000);
                } else {
                    message.error(response.msg);
                    if (captchaObj) {
                        captchaObj.reset();
                        captchaResult = null;
                    }
                }
            },
            error: function() {
                setLoading(false);
                message.error('服务器错误');
            }
        });
    };

    // 第三方登录
    const handleSocialLogin = (type) => {
        $.ajax({
            type: "POST",
            url: "ajax.php?act=connect",
            data: { type: type },
            dataType: 'json',
            success: function(data) {
                if(data.code === 0){
                    window.location.href = data.url;
                }else{
                    message.error(data.msg);
                }
            },
            error: function() {
                message.error('连接失败');
            }
        });
    };

    // 渲染用户名输入框
    const renderUsernameInput = () => {
        if (loginMode === 'password') {
            return (
                <Form.Item
                    name="username"
                    rules={[{ required: true, message: '请输入邮箱/手机号' }]}
                >
                    <Input
                        prefix={React.createElement('i', { className: 'fa fa-user' })}
                        placeholder="邮箱/手机号"
                        size="large"
                    />
                </Form.Item>
            );
        } else {
            return (
                <Form.Item
                    name="username"
                    rules={[{ required: true, message: '请输入商户ID' }]}
                >
                    <Input
                        prefix={React.createElement('i', { className: 'fa fa-user' })}
                        placeholder="商户ID"
                        size="large"
                    />
                </Form.Item>
            );
        }
    };

    // 构建表单元素数组
    const formElements = [];

    // 登录方式切换
    if (!config.closeKeyLogin) {
        formElements.push(React.createElement(Tabs, {
            activeKey: loginMode,
            onChange: setLoginMode,
            centered: true,
            items: [
                { key: 'password', label: '密码登录' },
                { key: 'key', label: '密钥登录' }
            ]
        }));
    }

    // 用户名输入框
    formElements.push(renderUsernameInput());

    // 密码输入框
    formElements.push(React.createElement(Form.Item, {
        name: 'password',
        rules: [{ required: true, message: loginMode === 'password' ? '请输入密码' : '请输入商户密钥' }]
    }, React.createElement(Input.Password, {
        prefix: React.createElement('i', { className: 'fa fa-lock' }),
        placeholder: loginMode === 'password' ? '密码' : '商户密钥',
        size: 'large'
    })));

    // 验证码
    if (config.captchaOpen) {
        formElements.push(React.createElement(Form.Item, null,
            React.createElement('div', { className: 'captcha-container' },
                captchaLoading ?
                    React.createElement('div', { style: { textAlign: 'center', padding: '20px' } },
                        React.createElement(Spin, { tip: '正在加载验证码...' })
                    ) :
                    React.createElement('div', { id: 'captcha-container' })
            )
        ));
    }

    // 记住登录状态
    formElements.push(React.createElement(Form.Item, null,
        React.createElement(Checkbox, null, '记住登录状态')
    ));

    // 登录按钮
    formElements.push(React.createElement(Form.Item, null,
        React.createElement(Button, {
            type: 'primary',
            htmlType: 'submit',
            loading: loading,
            block: true,
            size: 'large'
        }, '立即登录')
    ));

    // 找回密码和注册链接
    formElements.push(React.createElement(Form.Item, null,
        React.createElement(Space, { style: { width: '100%', justifyContent: 'space-between' } },
            React.createElement(Button, { type: 'link', href: 'findpwd.php' },
                React.createElement('i', { className: 'fa fa-unlock' }), ' 找回密码'
            ),
            config.regOpen ? React.createElement(Button, { type: 'link', href: 'reg.php' },
                React.createElement('i', { className: 'fa fa-user-plus' }), ' 注册商户'
            ) : null
        )
    ));

    // 构建主要内容数组
    const mainElements = [];

    // 添加表单
    mainElements.push(React.createElement.apply(React, [Form, {
        form: form,
        onFinish: handleLogin,
        layout: 'vertical',
        size: 'large'
    }].concat(formElements)));

    // 添加第三方登录
    if (!config.connectMode && (config.loginAlipay > 0 || config.loginQQ > 0 || config.loginWX > 0)) {
        const socialButtons = [];

        if (config.loginAlipay > 0 || config.loginAlipay == -1) {
            socialButtons.push(React.createElement(Button, {
                key: 'alipay',
                className: 'social-btn',
                onClick: function() { handleSocialLogin('alipay'); },
                title: '支付宝快捷登录'
            }, React.createElement('img', { src: '../assets/icon/alipay.ico', width: '20', alt: '支付宝' })));
        }

        if (config.loginQQ > 0) {
            socialButtons.push(React.createElement(Button, {
                key: 'qq',
                className: 'social-btn',
                onClick: function() { handleSocialLogin('qq'); },
                title: 'QQ快捷登录'
            }, React.createElement('i', { className: 'fa fa-qq', style: { color: '#0BB2FF', fontSize: '20px' } })));
        }

        if (config.loginWX > 0 || config.loginWX == -1) {
            socialButtons.push(React.createElement(Button, {
                key: 'wx',
                className: 'social-btn',
                onClick: function() { handleSocialLogin('wx'); },
                title: '微信快捷登录'
            }, React.createElement('i', { className: 'fa fa-wechat', style: { color: 'green', fontSize: '20px' } })));
        }

        mainElements.push(React.createElement('div', { className: 'social-login' },
            React.createElement(Divider, null, '第三方登录'),
            React.createElement.apply(React, [Space, null].concat(socialButtons))
        ));
    }

    return React.createElement.apply(React, ['div', null].concat(mainElements));
};

// 渲染应用
const root = ReactDOM.createRoot(document.getElementById('antd-login-app'));
root.render(<AntdLoginApp />);
</script>

<!-- 原有JavaScript逻辑保持兼容 -->
<script>
var captcha_open = <?php echo $conf['captcha_open_login']?>;
window.$ = window.jQuery;
// 兼容性函数 - 保持原有接口可用
function connect(type){
    // 这个函数现在由React组件处理，保留以防其他地方调用
    console.log('connect function called with type:', type);
}

function submitLogin(type, user, pass){
    // 这个函数现在由React组件处理，保留以防其他地方调用
    console.log('submitLogin function called');
}
</script>
</body>
</html>