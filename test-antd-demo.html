<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ant Design React CDN 演示页面 | PHP支付系统UI测试</title>
    
    <!-- Ant Design CSS -->
    <link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/reset.css">
    <link rel="stylesheet" href="https://unpkg.com/antd@5.12.8/dist/antd.min.css">
    
    <!-- 自定义样式 -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .demo-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .demo-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .demo-content {
            padding: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-section h2 {
            color: #1890ff;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .component-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .component-card {
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        
        .component-card h3 {
            margin-top: 0;
            color: #262626;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .login-demo {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .payment-demo {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 页面头部 -->
        <div class="demo-header">
            <h1>🚀 Ant Design React CDN 演示</h1>
            <p>PHP支付系统 UI 组件库集成测试 - 现代化界面设计预览</p>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="demo-content">
            <!-- React 组件挂载点 -->
            <div id="react-app"></div>
        </div>
    </div>

    <!-- React & Ant Design CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script crossorigin src="https://unpkg.com/dayjs@1.11.10/dayjs.min.js"></script>
    <script crossorigin src="https://unpkg.com/antd@5.12.8/dist/antd.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- React 组件代码 -->
    <script type="text/babel">
        const { useState, useEffect } = React;
        const { 
            Button, Card, Form, Input, Select, DatePicker, Table, 
            Statistic, Row, Col, Alert, Badge, Tag, Progress, 
            Tabs, Modal, message, Space, Divider, Typography,
            Steps, Radio, Checkbox, Switch, Slider, Rate
        } = antd;
        
        const { Title, Text, Paragraph } = Typography;
        const { Option } = Select;
        const { TabPane } = Tabs;

        // 模拟数据
        const mockPaymentData = [
            { key: '1', orderId: 'PAY001', amount: 299.00, status: 'success', method: '支付宝', time: '2025-08-09 14:30' },
            { key: '2', orderId: 'PAY002', amount: 158.50, status: 'pending', method: '微信支付', time: '2025-08-09 14:25' },
            { key: '3', orderId: 'PAY003', amount: 89.90, status: 'failed', method: '银行卡', time: '2025-08-09 14:20' },
            { key: '4', orderId: 'PAY004', amount: 1299.00, status: 'success', method: '支付宝', time: '2025-08-09 14:15' },
        ];

        // 主应用组件
        const AntdDemo = () => {
            const [loginForm] = Form.useForm();
            const [loading, setLoading] = useState(false);
            const [activeTab, setActiveTab] = useState('1');
            const [modalVisible, setModalVisible] = useState(false);

            // 登录表单提交
            const handleLogin = (values) => {
                setLoading(true);
                message.loading('登录中...', 1);
                
                setTimeout(() => {
                    setLoading(false);
                    message.success('登录成功！');
                    console.log('登录数据:', values);
                }, 1500);
            };

            // 支付表格列配置
            const paymentColumns = [
                {
                    title: '订单号',
                    dataIndex: 'orderId',
                    key: 'orderId',
                    render: (text) => <Text code>{text}</Text>
                },
                {
                    title: '金额',
                    dataIndex: 'amount',
                    key: 'amount',
                    render: (amount) => <Text strong>¥{amount}</Text>
                },
                {
                    title: '支付方式',
                    dataIndex: 'method',
                    key: 'method',
                    render: (method) => <Tag color="blue">{method}</Tag>
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: (status) => {
                        const statusConfig = {
                            success: { color: 'success', text: '成功' },
                            pending: { color: 'processing', text: '处理中' },
                            failed: { color: 'error', text: '失败' }
                        };
                        const config = statusConfig[status];
                        return <Badge status={config.color} text={config.text} />;
                    }
                },
                {
                    title: '时间',
                    dataIndex: 'time',
                    key: 'time'
                }
            ];

            return (
                <div>
                    {/* 功能概览 */}
                    <div className="demo-section">
                        <Title level={2}>📊 系统概览</Title>
                        <Row gutter={[16, 16]} className="stats-grid">
                            <Col span={6}>
                                <Card>
                                    <Statistic
                                        title="今日交易额"
                                        value={125689}
                                        precision={2}
                                        valueStyle={{ color: '#3f8600' }}
                                        prefix="¥"
                                        suffix="元"
                                    />
                                </Card>
                            </Col>
                            <Col span={6}>
                                <Card>
                                    <Statistic
                                        title="交易笔数"
                                        value={1128}
                                        valueStyle={{ color: '#1890ff' }}
                                        suffix="笔"
                                    />
                                </Card>
                            </Col>
                            <Col span={6}>
                                <Card>
                                    <Statistic
                                        title="成功率"
                                        value={98.5}
                                        precision={1}
                                        valueStyle={{ color: '#722ed1' }}
                                        suffix="%"
                                    />
                                </Card>
                            </Col>
                            <Col span={6}>
                                <Card>
                                    <Statistic
                                        title="活跃商户"
                                        value={456}
                                        valueStyle={{ color: '#fa8c16' }}
                                        suffix="个"
                                    />
                                </Card>
                            </Col>
                        </Row>
                    </div>

                    {/* 标签页演示 */}
                    <div className="demo-section">
                        <Title level={2}>🎛️ 功能模块演示</Title>
                        <Tabs activeKey={activeTab} onChange={setActiveTab}>
                            <TabPane tab="🔐 用户登录" key="1">
                                <div className="login-demo">
                                    <Title level={3} style={{ textAlign: 'center', marginBottom: 30 }}>
                                        商户登录
                                    </Title>
                                    <Form
                                        form={loginForm}
                                        layout="vertical"
                                        onFinish={handleLogin}
                                        size="large"
                                    >
                                        <Form.Item
                                            label="用户名"
                                            name="username"
                                            rules={[{ required: true, message: '请输入用户名' }]}
                                        >
                                            <Input 
                                                placeholder="邮箱/手机号/商户ID" 
                                                prefix={<i className="anticon anticon-user" />}
                                            />
                                        </Form.Item>
                                        
                                        <Form.Item
                                            label="密码"
                                            name="password"
                                            rules={[{ required: true, message: '请输入密码' }]}
                                        >
                                            <Input.Password 
                                                placeholder="请输入密码"
                                                prefix={<i className="anticon anticon-lock" />}
                                            />
                                        </Form.Item>
                                        
                                        <Form.Item>
                                            <Checkbox>记住登录状态</Checkbox>
                                        </Form.Item>
                                        
                                        <Form.Item>
                                            <Button 
                                                type="primary" 
                                                htmlType="submit" 
                                                loading={loading}
                                                block
                                                size="large"
                                            >
                                                立即登录
                                            </Button>
                                        </Form.Item>
                                        
                                        <div style={{ textAlign: 'center' }}>
                                            <Space split={<Divider type="vertical" />}>
                                                <Button type="link">忘记密码</Button>
                                                <Button type="link">注册账户</Button>
                                            </Space>
                                        </div>
                                    </Form>
                                </div>
                            </TabPane>
                            
                            <TabPane tab="💳 支付管理" key="2">
                                <Alert
                                    message="支付订单管理"
                                    description="实时查看和管理所有支付订单，支持多种支付方式和状态筛选"
                                    type="info"
                                    showIcon
                                    style={{ marginBottom: 20 }}
                                />
                                
                                <div className="payment-demo">
                                    <Space style={{ marginBottom: 16 }}>
                                        <Select defaultValue="all" style={{ width: 120 }}>
                                            <Option value="all">全部状态</Option>
                                            <Option value="success">成功</Option>
                                            <Option value="pending">处理中</Option>
                                            <Option value="failed">失败</Option>
                                        </Select>
                                        <Select defaultValue="all" style={{ width: 120 }}>
                                            <Option value="all">全部方式</Option>
                                            <Option value="alipay">支付宝</Option>
                                            <Option value="wechat">微信支付</Option>
                                            <Option value="bank">银行卡</Option>
                                        </Select>
                                        <Button type="primary">查询</Button>
                                        <Button>导出</Button>
                                    </Space>
                                    
                                    <Table 
                                        columns={paymentColumns} 
                                        dataSource={mockPaymentData}
                                        pagination={{ pageSize: 5 }}
                                        size="middle"
                                    />
                                </div>
                            </TabPane>
                            
                            <TabPane tab="⚙️ 系统设置" key="3">
                                <Row gutter={[24, 24]}>
                                    <Col span={12}>
                                        <Card title="基础设置" size="small">
                                            <Form layout="vertical" size="small">
                                                <Form.Item label="网站名称">
                                                    <Input defaultValue="PHP支付系统" />
                                                </Form.Item>
                                                <Form.Item label="系统状态">
                                                    <Switch defaultChecked />
                                                </Form.Item>
                                                <Form.Item label="注册开关">
                                                    <Radio.Group defaultValue="open">
                                                        <Radio value="open">开放注册</Radio>
                                                        <Radio value="invite">邀请注册</Radio>
                                                        <Radio value="close">关闭注册</Radio>
                                                    </Radio.Group>
                                                </Form.Item>
                                            </Form>
                                        </Card>
                                    </Col>
                                    <Col span={12}>
                                        <Card title="支付配置" size="small">
                                            <Form layout="vertical" size="small">
                                                <Form.Item label="手续费率 (%)">
                                                    <Slider defaultValue={2.5} min={0} max={10} step={0.1} />
                                                </Form.Item>
                                                <Form.Item label="服务评分">
                                                    <Rate defaultValue={4.5} allowHalf />
                                                </Form.Item>
                                                <Form.Item label="系统负载">
                                                    <Progress percent={75} status="active" />
                                                </Form.Item>
                                            </Form>
                                        </Card>
                                    </Col>
                                </Row>
                            </TabPane>
                        </Tabs>
                    </div>

                    {/* 组件展示 */}
                    <div className="demo-section">
                        <Title level={2}>🧩 UI 组件展示</Title>
                        <div className="component-showcase">
                            <div className="component-card">
                                <Title level={4}>按钮组件</Title>
                                <Space wrap>
                                    <Button type="primary">主要按钮</Button>
                                    <Button>默认按钮</Button>
                                    <Button type="dashed">虚线按钮</Button>
                                    <Button type="text">文本按钮</Button>
                                    <Button type="link">链接按钮</Button>
                                    <Button danger>危险按钮</Button>
                                </Space>
                            </div>
                            
                            <div className="component-card">
                                <Title level={4}>标签组件</Title>
                                <Space wrap>
                                    <Tag color="success">成功</Tag>
                                    <Tag color="processing">处理中</Tag>
                                    <Tag color="error">失败</Tag>
                                    <Tag color="warning">警告</Tag>
                                    <Tag color="blue">支付宝</Tag>
                                    <Tag color="green">微信支付</Tag>
                                </Space>
                            </div>
                            
                            <div className="component-card">
                                <Title level={4}>步骤条</Title>
                                <Steps current={1} size="small">
                                    <Steps.Step title="创建订单" />
                                    <Steps.Step title="支付确认" />
                                    <Steps.Step title="支付完成" />
                                </Steps>
                            </div>
                            
                            <div className="component-card">
                                <Title level={4}>操作演示</Title>
                                <Space>
                                    <Button 
                                        type="primary" 
                                        onClick={() => message.success('操作成功！')}
                                    >
                                        成功提示
                                    </Button>
                                    <Button 
                                        onClick={() => message.error('操作失败！')}
                                    >
                                        错误提示
                                    </Button>
                                    <Button 
                                        onClick={() => setModalVisible(true)}
                                    >
                                        打开弹窗
                                    </Button>
                                </Space>
                            </div>
                        </div>
                    </div>

                    {/* 弹窗演示 */}
                    <Modal
                        title="支付确认"
                        open={modalVisible}
                        onOk={() => {
                            message.success('支付成功！');
                            setModalVisible(false);
                        }}
                        onCancel={() => setModalVisible(false)}
                        okText="确认支付"
                        cancelText="取消"
                    >
                        <div style={{ textAlign: 'center', padding: '20px 0' }}>
                            <Title level={3}>¥299.00</Title>
                            <Paragraph>
                                订单号：PAY20250809001<br/>
                                商品：PHP支付系统授权<br/>
                                支付方式：支付宝
                            </Paragraph>
                        </div>
                    </Modal>
                </div>
            );
        };

        // 渲染应用
        const root = ReactDOM.createRoot(document.getElementById('react-app'));
        root.render(<AntdDemo />);
    </script>
</body>
</html>
