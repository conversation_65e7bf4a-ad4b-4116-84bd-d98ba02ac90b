// React组件 - 登录表单增强
// 使用Babel转换JSX，兼容现有PHP后端

const { useState, useEffect } = React;

// 输入框组件
const InputField = ({ type, name, placeholder, value, onChange, onKeyDown, className = "form-control no-border" }) => {
    const [focused, setFocused] = useState(false);
    
    return React.createElement('div', {
        className: `list-group-item ${focused ? 'focused' : ''}`
    }, React.createElement('input', {
        type,
        name,
        placeholder,
        value,
        onChange,
        onKeyDown,
        onFocus: () => setFocused(true),
        onBlur: (e) => setFocused(!!e.target.value),
        className
    }));
};

// 提交按钮组件
const SubmitButton = ({ loading, onClick, children, className = "btn btn-lg btn-primary btn-block" }) => {
    return React.createElement('button', {
        type: 'button',
        className,
        onClick,
        disabled: loading
    }, loading ? 
        React.createElement('span', null, 
            React.createElement('i', { className: 'fa fa-spinner fa-spin' }), 
            ' 登录中...'
        ) : children
    );
};

// 登录表单组件
const LoginForm = ({ csrfToken, loginType, captchaOpen }) => {
    const [formData, setFormData] = useState({
        type: loginType || 1,
        user: '',
        pass: ''
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    const handleInputChange = (field) => (e) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value
        }));
        // 清除对应字段的错误
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const handleKeyDown = (e) => {
        if (e.keyCode === 13) {
            handleSubmit();
        }
    };

    const validateForm = () => {
        const newErrors = {};
        if (!formData.user.trim()) {
            newErrors.user = formData.type === 1 ? '邮箱/手机号不能为空' : '商户ID不能为空';
        }
        if (!formData.pass.trim()) {
            newErrors.pass = formData.type === 1 ? '密码不能为空' : '商户密钥不能为空';
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (!validateForm()) {
            return;
        }

        if (captchaOpen && !window.$.captchaResult) {
            layer.alert('请先完成滑动验证！');
            return;
        }

        setLoading(true);
        
        // 调用原有的提交逻辑
        if (window.submitLogin) {
            window.submitLogin(formData.type, formData.user, formData.pass);
        }

        // 3秒后重置加载状态
        setTimeout(() => {
            setLoading(false);
        }, 3000);
    };

    return React.createElement('div', { className: 'list-group list-group-sm swaplogin' },
        React.createElement('input', {
            type: 'hidden',
            name: 'type',
            value: formData.type
        }),
        React.createElement(InputField, {
            type: 'text',
            name: 'user',
            placeholder: formData.type === 1 ? '邮箱/手机号' : '商户ID',
            value: formData.user,
            onChange: handleInputChange('user'),
            onKeyDown: handleKeyDown
        }),
        errors.user && React.createElement('div', { 
            className: 'text-danger small', 
            style: { padding: '5px 15px' } 
        }, errors.user),
        React.createElement(InputField, {
            type: 'password',
            name: 'pass',
            placeholder: formData.type === 1 ? '密码' : '商户密钥',
            value: formData.pass,
            onChange: handleInputChange('pass'),
            onKeyDown: handleKeyDown
        }),
        errors.pass && React.createElement('div', { 
            className: 'text-danger small', 
            style: { padding: '5px 15px' } 
        }, errors.pass),
        // 验证码容器（如果需要）
        captchaOpen && React.createElement('div', {
            className: 'list-group-item',
            id: 'captcha',
            style: { margin: 'auto' }
        }, 
            React.createElement('div', { id: 'captcha_text' }, '正在加载验证码'),
            React.createElement('div', { id: 'captcha_wait' },
                React.createElement('div', { className: 'loading' },
                    React.createElement('div', { className: 'loading-dot' }),
                    React.createElement('div', { className: 'loading-dot' }),
                    React.createElement('div', { className: 'loading-dot' }),
                    React.createElement('div', { className: 'loading-dot' })
                )
            )
        ),
        React.createElement('div', { id: 'captchaform' }),
        React.createElement('div', { style: { marginTop: '15px' } },
            React.createElement(SubmitButton, {
                loading,
                onClick: handleSubmit
            }, '立即登录')
        )
    );
};

// 初始化React组件
function initLoginReactComponent() {
    const formContainer = document.querySelector('.swaplogin');
    if (formContainer && typeof React !== 'undefined') {
        // 获取现有配置
        const csrfToken = document.querySelector('input[name="csrf_token"]')?.value;
        const loginType = document.querySelector('input[name="type"]')?.value || 1;
        const captchaOpen = document.getElementById('captcha') ? 1 : 0;
        
        // 创建React根容器
        const reactContainer = document.createElement('div');
        reactContainer.id = 'react-login-form';
        
        // 替换原有表单
        formContainer.parentNode.replaceChild(reactContainer, formContainer);
        
        // 渲染React组件
        const root = ReactDOM.createRoot(reactContainer);
        root.render(React.createElement(LoginForm, {
            csrfToken,
            loginType: parseInt(loginType),
            captchaOpen
        }));
        
        console.log('React登录组件已初始化');
    }
}

// 导出初始化函数
window.initLoginReactComponent = initLoginReactComponent;
